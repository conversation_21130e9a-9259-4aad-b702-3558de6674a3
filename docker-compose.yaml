services:
  traefik:
    image: traefik:v3.5
    container_name: traefik
    command:
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.letsencrypt.acme.email=${ACME_EMAIL}"
      - "--certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json"
      - "--certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=web"
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - "./letsencrypt:/letsencrypt"
    restart: unless-stopped

  codeocr:
    image: ghcr.io/jeremy-feng/codeocr:latest
    container_name: codeocr
    expose:
      - "8000"
    ports:
      - "3000:3000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_API_BASE=${OPENAI_API_BASE}
      - MODEL_NAME=${MODEL_NAME}
      - BACKEND_URL=http://codeocr:8000
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.codeocr-frontend.rule=Host(`codeocr.fengchao.pro`)"
      - "traefik.http.routers.codeocr-frontend.entrypoints=web"
      - "traefik.http.routers.codeocr-frontend.middlewares=redirect-to-https"
      - "traefik.http.routers.codeocr-frontend-secure.rule=Host(`codeocr.fengchao.pro`)"
      - "traefik.http.routers.codeocr-frontend-secure.entrypoints=websecure"
      - "traefik.http.routers.codeocr-frontend-secure.tls.certresolver=letsencrypt"
      - "traefik.http.routers.codeocr-frontend-secure.service=codeocr-frontend"
      - "traefik.http.services.codeocr-frontend.loadbalancer.server.port=3000"
      - "traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https"
