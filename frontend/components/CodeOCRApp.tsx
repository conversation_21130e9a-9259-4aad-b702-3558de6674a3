"use client";

import APISettingsDialog from "@/components/APISettingsDialog";
import ImageUpload from "@/components/ImageUpload";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import type { BundledLanguage } from "@/components/ui/kibo-ui/code-block";
import {
  CodeBlock,
  CodeBlockBody,
  CodeBlockContent,
  CodeBlockCopyButton,
  CodeBlockFilename,
  CodeBlockFiles,
  CodeBlockHeader,
  CodeBlockItem,
} from "@/components/ui/kibo-ui/code-block";
import { Loader2, ScanText, Code, Square } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useState, useRef } from "react";
import { toast } from "sonner";

interface CodeOCRResponse {
  code: string;
}

interface APISettings {
  apiKey: string;
  apiBase: string;
  modelName: string;
}

export default function CodeOCRApp() {
  const t = useTranslations("CodeOCR");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<CodeOCRResponse | null>(null);
  const [partialContent, setPartialContent] = useState("");
  const [apiSettings, setApiSettings] = useState<APISettings>({
    apiKey: "",
    apiBase: "",
    modelName: "",
  });
  const abortControllerRef = useRef<AbortController | null>(null);

  const handleFileSelect = useCallback((file: File) => {
    setSelectedFile(file);
    setResult(null);
  }, []);

  const handleRemoveFile = useCallback(() => {
    setSelectedFile(null);
    setResult(null);
  }, []);

  const processImage = useCallback(async () => {
    if (!selectedFile) {
      toast.error(t("selectImageFirst"));
      return;
    }

    // Get environment variables as fallback
    const envApiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY || "";
    const envApiBase = process.env.NEXT_PUBLIC_OPENAI_API_BASE || "";
    const envModelName = process.env.NEXT_PUBLIC_MODEL_NAME || "";

    // Use user settings or fallback to environment variables
    const finalApiKey = apiSettings.apiKey || envApiKey;
    const finalApiBase =
      apiSettings.apiBase || envApiBase || "https://api.openai.com/v1";
    const finalModelName = apiSettings.modelName || envModelName;

    // Validate that we have all required settings
    if (!finalApiKey) {
      toast.error(t("apiKeyRequired"));
      return;
    }
    if (!finalModelName) {
      toast.error(t("modelNameRequired"));
      return;
    }

    setIsProcessing(true);
    setResult(null);
    setPartialContent("");

    // Create abort controller for cancellation
    abortControllerRef.current = new AbortController();

    const reader = new FileReader();
    reader.readAsDataURL(selectedFile);
    reader.onload = async () => {
      // File will be sent directly via FormData

      try {
        // Create FormData for the Next.js API route
        const formData = new FormData();
        formData.append("file", selectedFile);
        formData.append("api_key", finalApiKey);
        formData.append("api_base", finalApiBase);
        formData.append("model_name", finalModelName);

        const response = await fetch("/api/codeocr", {
          method: "POST",
          body: formData,
          signal: abortControllerRef.current?.signal,
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error("No reader available");
        }

        const decoder = new TextDecoder();
        let buffer = "";
        let accumulated = "";

        while (true) {
          const { done, value } = await reader.read();

          if (done) break;

          buffer += decoder.decode(value, { stream: true });

          // Process complete messages
          const lines = buffer.split("\n");
          buffer = lines.pop() || ""; // Keep incomplete line in buffer

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              try {
                const data = JSON.parse(line.slice(6));

                if (data.type === "content") {
                  accumulated += data.content;
                  setPartialContent(accumulated);
                } else if (data.type === "complete") {
                  // Keep the final content in result for completed state
                  setResult({ code: accumulated });
                  // Keep partialContent for display consistency
                  // Don't clear partialContent to avoid content disappearing
                  toast.success(t("extractSuccess"));
                  break;
                } else if (data.type === "error") {
                  throw new Error(data.content);
                }
              } catch (e) {
                console.error("Error parsing data:", e);
              }
            }
          }
        }
      } catch (err: any) {
        if (err.name === "AbortError") {
          toast.info("Processing cancelled");
        } else {
          const errorMessage =
            err instanceof Error ? err.message : t("extractFailed");
          toast.error(errorMessage);
          setResult({
            code: `\`\`\`text\n--- ERROR ---\n${errorMessage}\n\`\`\``,
          });
        }
      } finally {
        setIsProcessing(false);
        abortControllerRef.current = null;
      }
    };
    reader.onerror = () => {
      toast.error(t("imageReadFailed"));
      setIsProcessing(false);
    };
  }, [selectedFile, apiSettings, t]);

  const stopProcessing = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  // Helper function to parse language and code from the markdown block
  const parseCodeBlock = (rawCode: string | undefined) => {
    if (!rawCode) {
      return { language: "text", code: "" };
    }
    const match = rawCode.match(/^```(\w+)\n([\s\S]*)\n```$/);
    if (match) {
      return { language: match[1], code: match[2] };
    }
    // Fallback for raw text or malformed blocks
    return { language: "text", code: rawCode };
  };

  // Always prefer partialContent if available, fallback to result.code
  const displayContent = partialContent || result?.code;
  const { language, code } = parseCodeBlock(displayContent);

  // Debug logging
  console.log("Debug info:", {
    isProcessing,
    partialContentLength: partialContent.length,
    resultCode: result?.code?.substring(0, 100) + "...",
    displayContentLength: displayContent?.length,
    language,
    codeLength: code.length,
  });

  // Ensure we always have content to display
  const finalCode = code || displayContent;

  return (
    <div className="w-full space-y-4">
      <Card className="shadow-md">
        <CardContent className="p-4 sm:p-6">
          <div className="space-y-4 sm:space-y-6">
            <ImageUpload
              onFileSelect={handleFileSelect}
              onRemoveFile={handleRemoveFile}
              selectedFile={selectedFile}
              disabled={isProcessing}
            />

            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              <div className="w-full sm:w-auto">
                <APISettingsDialog onSettingsChange={setApiSettings} />
              </div>

              <div className="flex gap-2 w-full sm:flex-1">
                <Button
                  onClick={processImage}
                  disabled={!selectedFile || isProcessing}
                  className="flex-1"
                  size="default"
                  variant="default"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t("processing")}
                    </>
                  ) : (
                    <>
                      <ScanText className="mr-2 h-4 w-4" />
                      {t("extractCode")}
                    </>
                  )}
                </Button>

                {isProcessing && (
                  <Button
                    onClick={stopProcessing}
                    variant="destructive"
                    size="default"
                  >
                    <Square className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Section */}
      {displayContent && finalCode ? (
        <CodeBlock
          data={[
            {
              language: language,
              filename: "",
              code: finalCode,
            },
          ]}
          value={language}
          defaultValue={language}
        >
          <CodeBlockHeader>
            <CodeBlockFiles>
              {(item) => (
                <CodeBlockFilename key={item.language} value={item.language}>
                  <div className="flex items-center gap-2">
                    <Code /> {t("extractedCode")}
                    {isProcessing && (
                      <Loader2 className="h-3 w-3 animate-spin ml-1" />
                    )}
                  </div>
                </CodeBlockFilename>
              )}
            </CodeBlockFiles>
            <CodeBlockCopyButton value={finalCode} />
          </CodeBlockHeader>
          <CodeBlockBody>
            {(item) => (
              <CodeBlockItem key={item.language} value={item.language}>
                <CodeBlockContent language={item.language as BundledLanguage}>
                  {item.code}
                </CodeBlockContent>
              </CodeBlockItem>
            )}
          </CodeBlockBody>
        </CodeBlock>
      ) : null}

      {/* No code message */}
      {result && !code && (
        <div className="border rounded-lg p-8 text-center text-gray-500 dark:text-gray-400">
          {t("noCodeDetected")}
        </div>
      )}
    </div>
  );
}
