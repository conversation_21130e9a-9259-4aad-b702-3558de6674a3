import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    // Get the form data from the request
    const formData = await request.formData();

    // Check if formData contains a file
    const imageFile = formData.get("file");
    if (!imageFile || typeof imageFile === 'string') {
      return NextResponse.json(
        { error: "No image file provided" },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
    if (!allowedTypes.includes(imageFile.type)) {
      return NextResponse.json(
        {
          error:
            "Invalid file type. Please upload PNG, JPG, JPEG, or WebP images.",
        },
        { status: 400 }
      );
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (imageFile.size > maxSize) {
      return NextResponse.json(
        { error: "File size exceeds 5MB limit." },
        { status: 400 }
      );
    }

    // Convert file to base64 for the FastAPI backend
    const arrayBuffer = await imageFile.arrayBuffer();
    const base64 = Buffer.from(arrayBuffer).toString("base64");
    const mimeType = imageFile.type;
    const base64DataUrl = `data:${mimeType};base64,${base64}`;

    // Get required API settings from form data
    const apiKey = formData.get("api_key") as string | null;
    const apiBase = formData.get("api_base") as string | null;
    const modelName = formData.get("model_name") as string | null;

    // Validate required API settings
    if (!apiKey) {
      return NextResponse.json(
        { error: "API key is required. Please configure your API settings." },
        { status: 400 }
      );
    }

    if (!modelName) {
      return NextResponse.json(
        {
          error: "Model name is required. Please configure your API settings.",
        },
        { status: 400 }
      );
    }

    // Prepare the JSON payload for the FastAPI backend
    const payload = {
      image_source: base64DataUrl,
      api_key: apiKey,
      api_base: apiBase || "https://api.openai.com/v1",
      model_name: modelName,
    };

    // Forward the request to the FastAPI backend
    const backendUrl = process.env.BACKEND_URL || "http://127.0.0.1:8000";

    const response = await fetch(`${backendUrl}/api/codeocr`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        { error: errorData.detail || "Backend processing failed" },
        { status: response.status }
      );
    }

    // Return the streaming response directly
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        "Content-Type": "text/plain",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "*",
      },
    });
  } catch (error) {
    console.error("Error in CodeOCR API route:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
