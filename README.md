# CodeOCR

Convert code screenshots to copyable text using AI-powered OCR technology.

## Features

- Upload images via drag & drop, click, or paste from clipboard
- AI-powered code extraction
- Automatic language detection and syntax highlighting
- One-click copy to clipboard

![usage-1](frontend/public/usage-1.png)

Convert it to copyable code text:

![usage-2](frontend/public/usage-2.png)

```python
def hello_world() -> None:
    print("Hello World from Code0CR!")
```

## Deployment

### Installation

1. Clone the repository:

```bash
git clone https://github.com/jeremy-feng/codeocr.git
cd codeocr
```

2. Install frontend dependencies:

```bash
cd frontend
pnpm install
```

3. Install backend dependencies:

```bash
cd backend
pip install -r requirements.txt
```

### Running

Start both frontend and backend from their respective directories:

```bash
# In a new terminal, start the backend (port 8000)
cd backend
python main.py

# In another terminal, start the frontend (port 3000)
cd frontend
pnpm run dev
```

## License

This project is open source and available under the [MIT License](LICENSE).
